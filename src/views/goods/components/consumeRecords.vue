<template>
  <div class="table-wrap">
    <header>
      <Input v-model="postData.search" placeholder="商品名称" style="width: 140px" @on-enter="doSearch" />
      <DatePicker type="daterange" v-model="dateRange" @on-change="dateChange" placeholder="时间段" style="width: 200px"  :clearable="false"></DatePicker>
      <PayTypeSelect v-model="postData.pay_type" style="width: 140px" @on-change="handlePayTypeChange" :needShowDragonFly="false" showCardPay></PayTypeSelect>
      <Select v-model="postData.pay_status" placeholder="状态" style="width: 140px" clearable>
        <Option :value="1">已支付</Option>
        <Option :value="2">挂账</Option>
        <Option :value="3">已取消</Option>
      </Select>
      <Select v-model="postData.purchase_channel" placeholder="购买方式" style="width: 140px" clearable>
        <Option :value="0">前台</Option>
        <Option :value="1">会员端</Option>
      </Select>
      <Select v-model="postData.consume_status" placeholder="核销状态" style="width: 140px" clearable>
        <Option :value="0">待核销</Option>
        <Option :value="1">已核销</Option>
      </Select>
      <Button type="success" @click="doSearch">搜索</Button>
    </header>
    <Table ref="table" :data="tableData" :columns="columns" disabledHover stripe></Table>
    <div class="total">
      <p>当页小计:
        <span>{{amount}}</span> 元</p>
      <p>总计:
        <span>{{totalAmount}}</span> 元</p>
    </div>
    <footer>
      <Button v-if="$store.state.adminInfo.commodity_inventory.consumption_log_export" @click="exportCsv">导出Excel</Button>
      <Pager :total="total" :postData="postData" @on-change="pageChange"></Pager>
    </footer>
    <Modal title="销账" v-model="showModal" :mask-closable="false">
      <Form ref="formRef" :model="salesAccount" :label-width="80">
        <FormItem label="支付方式" prop="pay_type" :rules="{ required: true, type: 'number', message: '请选择销账方式' }">
          <PayTypeSelect v-model="salesAccount.pay_type" @on-change="handlePayTypeChange" :serviceType="1" @on-dragonfly-confirm="onDragonflyConfirm" :userId="salesAccount.user_id" :amount="salesAccount.total_price" showCardPay></PayTypeSelect>
        </FormItem>
        <template v-if="salesAccount.pay_type == 8">
          <FormItem label="会员卡" prop="card_user_id" :rules="{required: true, message: '请选择会员卡'}">
            <Select v-model="salesAccount.card_user_id" @on-change="handleCardChange">
              <Option v-for="item in userCardList" :key="item.card_user_id" :value="item.card_user_id">{{item.card_name}} ({{item.amount}})</Option>
            </Select>
             <Checkbox v-if="isShowDiscount" :true-value="1" :false-value="0" v-model="salesAccount.sign_discount" >启用储值卡折扣 {{curCardInfo.sign_discount}}折</Checkbox>
          </FormItem>
        </template>
        <FormItem label="备注">
          <Input placeholder="选填" v-model="salesAccount.remark" />
        </FormItem>
      </Form>
      <div slot="footer" class="modal-buttons">
        <Button type="success" @click="doSalesAccount">确定</Button>
        <Button @click="showModal = false">取消</Button>
      </div>
    </Modal>
    <receipt-modal v-model="showPrint" :to-path='toPath' />
    <ReturnPayModal notDate :payType="backData.pay_type" :showCardPay="true" :showMark="true" v-model="showBackModal" @on-confirm="backConfirm" />
  </div>
</template>

<script>
  import Pager from 'components/pager';
  import PayTypeSelect from 'components/form/PayTypeSelect';
  import { formatDate } from 'utils';
  import receiptModal from 'components/receipt/receipt.vue';
  import receipt from 'mixins/receipt.js';
  import ReturnPayModal from 'components/form/ReturnPayModal'
  const PAY_STATUS = ['已支付', '挂账', '已取消'];

  export default {
    name: 'consumeRecords',
    mixins: [receipt],
    components: {
      Pager,
      PayTypeSelect,
      ReturnPayModal,
      receiptModal
    },
    data() {
      return {
        showModal: false,
        showBackModal: false,
        curCardInfo: '',
        card: '',
        sale: '',
        dateRange: [formatDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')],
        postData: {
          search: '',
          pay_type: '',
          s_date: formatDate(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), 'yyyy-MM-dd'),
          e_date: formatDate(new Date(), 'yyyy-MM-dd'),
          pay_status: '',
          page_no: 1,
          page_size: 10
        },
        backData: {
          id: '',
          pay_type: '',
          // TODO: refund_date
        },
        userCardList: [],
        salesAccount: {
          id: '',
          total_price: '',
          card_user_id: '',
          pay_order_ids: [],
          pay_type: 3,
          remark: '',
          sign_discount: 0,
          user_id: ''
        },
        tableData: [],
        amount: '',
        totalAmount: '',
        total: '',
        columns: [
          {
            key: 'create_time_date',
            title: '购买时间',
            width: '150'
          },
          {
            key: 'commodity_name',
            title: '商品名称'
          },
          {
            key: 'commodity_price',
            title: '单价'
          },
          {
            key: 'purchase_count',
            title: '数量'
          },
          {
            key: 'total_price',
            title: '实收'
          },
          {
            key: 'payStatus',
            title: '状态'
          },
          {
            key: 'pay_type_name',
            title: '支付方式'
          },
          {
            key: 'purchase_channel',
            title: '购买方式',
            // 购买方式 0前台 1会员端
            render: (h, params) => {
              const item = params.row;
              return <div>{item.purchase_channel == 0 ? '前台' : '会员端'}</div>;
            }
          },
          {
            key: 'consume_status',
            title: '核销状态',
            // 核销状态0待核销 1已核销
            render: (h, params) => {
              const item = params.row;
              if (item.consume_status == 0) {
                return <Tag color="red">待核销</Tag>;
              } else if (item.consume_status == 1) {
                return <Tag color="green">已核销</Tag>;
              }
            },
            renderHeader: (h, params) => {
              return <Tooltip content="核销后库存才减少">
                核销状态
                <Icon size="16" type="ios-help-circle"  style="color: #f4a627;padding-left: 5px"/>
              </Tooltip>;
            }
          },
          {
            key: 'name',
            title: '会员姓名',
            render: (h, params) => {
              const item = params.row;
              // const url = `#/member/detail/${item.user_id}`;
              return item.name
              ? <a onClick={() => {
                  this.$router.push(`/member/detail/${item.user_id}`)
                }}>{item.name}</a>
              : <div>---</div>;
            }
          },
          {
            key: 'mark',
            title: '备注',
            render: (h, params) => {
              const item = params.row;
              return <div title={item.mark}>{item.mark}</div>;
            }
          },
          {
            key: 'operator_name',
            title: '操作账号'
          },
          {
            title: '操作',
            render: (h, params) => {
              const item = params.row;
              return item.pay_status != 3 ? (
                <div>
                  <i-button
                    type="text"
                    onClick={() => {
                      this.handleCancel(item);
                    }}>
                    取消
                  </i-button>
                  {item.pay_status == 2 ? (
                    <i-button
                      onClick={() => {
                        this.handleSalesAccount(item);
                      }}
                      type="text"
                      style="margin-left: 5px">
                      销账
                    </i-button>
                  ) : (
                    ''
                  )}
                </div>
              ) : (
                <div>---</div>
              );
            }
          }
        ]
      };
    },
    watch: {
      isShowDiscount(val, oldVal) {
        if(!val && oldVal && this.salesAccount.sign_discount) {
          this.salesAccount.sign_discount = 0
        }
      },
      showModal(val) {
        if (!val) {
          this.salesAccount = {
            id: '',
            total_price: '',
            pay_order_ids: [],
            card_user_id: '',
            pay_type: 3,
            remark: '',
            user_id: ''
          };
        }
      }
    },
    computed: {
      isShowDiscount() {
        return this.salesAccount.pay_type == 8 && this.curCardInfo && this.curCardInfo.sign_discount && this.curCardInfo.sign_discount!=='10.0'
      }
    },
    methods: {
      onDragonflyConfirm(info) {
        this.salesAccount.pay_order_ids = info.pay_order_ids
      },
      handleCardChange(val) {
        if(!val) {
          this.curCardInfo = null
        }
        for (const iterator of this.userCardList) {
          if(val === iterator.card_user_id) {
            this.curCardInfo = iterator
          }
        }
      },
      handlePayTypeChange(type) {
        if (type == 8) {
          this.getUserCard(this.salesAccount.user_id);
        }
      },
      dateChange([s_date, e_date]) {
        this.postData = { ...this.postData, ...{ s_date, e_date } };
      },
      pageChange(postData) {
        const { s_date, e_date } = postData;
        this.dateRange = [s_date, e_date];
        this.postData = { ...this.postData, ...postData };
        this.getList();
      },
      handleCancel(info) {
        this.backData.id = info.id
        if(info.pay_status == 2) {
          this.$Modal.confirm({
            title: '取消到店',
          content: '确定取消？',
          onOk: () => {
            this.backConfirm(info, true);
          }
        })
        } else {
          this.backData.pay_type = info.pay_type
          this.showBackModal = true
        }

      },
      backConfirm(info, noMark) {
        this.backData.pay_type = info.pay_type
        this.backData.mark = noMark ? '' : info.mark
        this.$service
          .post('/Web/Commodity/cancel_commodity_buy', this.backData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.showBackModal = false
              this.getList();
              this.$Message.success('取消成功');
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getUserCard(user_id) {
        if (!user_id) return;
        const url = 'Web/Commodity/get_user_debit_card';
        this.$service
          .post(url, { user_id })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.userCardList = data.list;
              if (!data.list.length) {
                this.$Message.error('该会员没有储值卡');
                return;
              }
              this.salesAccount.card_user_id = data.list[0].card_user_id;
              this.handleCardChange(data.list[0].card_user_id)
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleSalesAccount(info) {
        this.salesAccount.id = info.id;
        this.salesAccount.user_id = info.user_id;
        this.salesAccount.total_price = info.total_price;
        this.showModal = true;
      },
      doSalesAccount() {
        this.$refs.formRef.validate(valid => {
          if (valid) {
            const url = '/Web/Commodity/commodity_del_remind';
            this.$service
              .post(url, this.salesAccount)
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.getList();
                  this.showModal = false;
                  this.commodityComplete(this.salesAccount.user_id, res.data.order_sn, 'backpay');
                } else {
                  this.$Message.error(res.data.errormsg);
                }
              })
              .catch(err => {
                console.error(err);
              });
          }
        })
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      getList() {
        const url = '/Web/ConsumptionLog/get_consumption_log_list';
        this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.tableData = data.list.map(item => {
                item.operator_name = item.operator_name || '无';
                return {
                  ...item,
                  ...{
                    payStatus: PAY_STATUS[item.pay_status - 1],
                    pay_type_name: this.$store.getters['pay/getPayNameById'](item.pay_type)
                  }
                };
              });
              if (!data.list || !data.list.length) {
                this.$Message.error('未查询到数据');
                this.amount = 0;
                this.totalAmount = 0;
                return;
              }
              this.amount = Number(data.total_price_current_page).toFixed(2);
              this.totalAmount = data.total_price_all;
              this.total = data.count;
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getExportData() {
        return this.$service
          .post('/Web/ConsumptionLog/get_consumption_log_list', {
            ...this.postData,
            ...{ page_size: this.total, page_no: 1 }
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              return data.list.map(item => {
                return {
                  ...item,
                  ...{
                    payStatus: PAY_STATUS[item.pay_status - 1],
                    pay_type_name: this.$store.getters['pay/getPayNameById'](item.pay_type),
                    commodity_price: `${Number(item.commodity_price).toFixed(2)}元`
                  }
                };
              });
            } else {
              this.$Message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      async exportCsv() {
        const exportData = await this.getExportData();
        this.$refs.table.exportCsv({
          filename: !this.postData.s_date
            ? `商品销售记录`
            : `商品销售记录(${this.postData.s_date}~${this.postData.e_date})`,
          columns: this.columns.filter((col, index) => col.key),
          data: exportData
        });
      }
    }
  };
</script>

<style scoped lang="less">
  .select {
    width: 150px;
  }
  .total {
    font-size: 16px;
    padding: 20px 20px 0;
    display: flex;
    justify-content: flex-end;
    > p {
      padding-left: 60px;
      padding-right: 20px;
      span {
        font-weight: bold;
      }
    }
  }
  .table-wrap {
    border-top: 0;
  }
</style>
